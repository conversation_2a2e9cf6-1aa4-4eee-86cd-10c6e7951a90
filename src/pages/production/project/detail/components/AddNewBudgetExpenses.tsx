import { <PERSON><PERSON>, Col, Drawer, Form, InputNumber, Radio, Row, Space, Switch, message } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import {
  BUDGET_GROUPS,
  BUDGET_SUBCATEGORY_CONFIG,
  BudgetCategory,
  BudgetSubcategory,
  getBudgetSubcategoryLabel,
} from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionBudgetItem } from '../store'

// 根据分组生成费用项列表初始值
const generateGroupedExpenseList = () => {
  const groupedList: Record<string, IExpenseFormItem[]> = {}

  BUDGET_GROUPS.forEach(group => {
    groupedList[group.key] = []
    group.subcategories.forEach(subcategory => {
      const subcategoryConfig = BUDGET_SUBCATEGORY_CONFIG[subcategory]
      if (subcategoryConfig) {
        groupedList[group.key].push({
          category: subcategoryConfig.category,
          subcategory: subcategory,
          quotedPrice: undefined,
          personCount: undefined,
          dayCount: undefined,
          totalPrice: undefined,
          hasInvoice: false,
        })
      }
    })
  })

  return groupedList
}



interface IExpenseFormItem {
  category: BudgetCategory
  subcategory: BudgetSubcategory
  quotedPrice?: number
  personCount?: number
  dayCount?: number
  totalPrice?: number
  hasInvoice: boolean
}

interface IAddNewBudgetExpensesProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  loading?: boolean
  productionId: number
  project?: IProductionListItem
  checkExpenseExists?: (category: BudgetCategory, subcategory: BudgetSubcategory) => boolean // 检查费用项是否已存在
}

const AddNewBudgetExpenses: React.FC<IAddNewBudgetExpensesProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
  project,
  checkExpenseExists,
}) => {
  const [form] = Form.useForm()
  const [activeTab, setActiveTab] = useState('production')
  const { saveBudgetItems } = useProjectDetailStore()

  // 使用 useMemo 缓存分组数据
  const groupedExpenseList = useMemo(() => generateGroupedExpenseList(), [])

  useEffect(() => {
    if (open) {
      form.resetFields()
      setActiveTab('production') // 重置到第一个 tab
      setTimeout(() => {
        // 初始化第一个分组的费用项
        form.setFieldsValue({ expenseList: groupedExpenseList['production'] || [] })
      }, 50)
    } else {
      form.setFieldsValue({ expenseList: [] })
    }
  }, [open, form, groupedExpenseList])

  // 处理 Tab 切换
  const handleTabChange = (e: any) => {
    if (e?.target?.value) {
      setActiveTab(e.target.value)
      form.setFieldsValue({ expenseList: groupedExpenseList[e.target.value] || [] })
    }
  }



  // 处理保存
  const handleSave = async (values: any) => {
    try {
      const { expenseList } = values

      if (!expenseList || expenseList.length === 0) {
        message.warning('请至少添加一个费用项')
        return
      }

      // 过滤出有效的费用记录（有单价和总价的）
      const validExpenses = expenseList.filter((expense: IExpenseFormItem) =>
        expense.totalPrice && expense.totalPrice > 0
      )

      if (validExpenses.length === 0) {
        message.warning('请至少填写一个费用项的总价')
        return
      }

      // 检查费用项是否重复
      if (checkExpenseExists) {
        const duplicateExpenses = validExpenses.filter((expense: IExpenseFormItem) =>
          checkExpenseExists(expense.category, expense.subcategory)
        )

        if (duplicateExpenses.length > 0) {
          const duplicateNames = duplicateExpenses.map((expense: IExpenseFormItem) =>
            getBudgetSubcategoryLabel(expense.subcategory)
          ).join('、')
          message.error(`以下费用项已存在：${duplicateNames}`)
          return
        }
      }

      const budgetItems: IPrProductionBudgetItem[] = validExpenses.map((expense: IExpenseFormItem) => ({
        productionId,
        isPackage: false,
        quotedPrice: expense.quotedPrice||0,
        personCount: expense.personCount||0,
        dayCount: expense.dayCount||0,
        totalPrice: expense.totalPrice,
        hasInvoice: expense.hasInvoice ? 1 : 0,
        itemsDetail: [
          {
            itemId: 0, // 新增时为0
            category: expense.category,
            subcategory: expense.subcategory,
            personCount: expense.personCount||0,
          },
        ],
      }))

      const success = await saveBudgetItems({
        productionId,
        budgetItems,
      })

      if (success) {
        message.success(`成功添加 ${validExpenses.length} 个费用项`)
        onSuccess()
        onCancel()
      } else {
        message.error('添加费用项失败')
      }
    } catch (error) {
      console.error('保存费用项失败:', error)
      message.error('添加费用项失败')
    }
  }



  return (
    <Drawer
      title="批量添加费用项"
      open={open}
      onClose={onCancel}
      width={1200}
      extra={
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={() => form.submit()}>
            保存
          </Button>
        </Space>
      }>
      <Form form={form} layout="vertical" onFinish={handleSave}>
        {/* Tab 切换 */}
        <Form.Item label="选择费用分组">
          <Radio.Group value={activeTab} onChange={handleTabChange}>
            <Row gutter={[16, 16]}>
              {BUDGET_GROUPS.map(group => (
                <Col span={6} key={group.key}>
                  <Radio.Button value={group.key} style={{ width: '100%', textAlign: 'center' }}>
                    {group.label}
                  </Radio.Button>
                </Col>
              ))}
            </Row>
          </Radio.Group>
        </Form.Item>

        {/* 费用项列表 */}
        <Form.List name="expenseList">
          {(fields) => (
            <>
              {fields.map(({ key, name, ...restField }) => {
                const currentExpense = form.getFieldValue(['expenseList', name])
                const subcategoryConfig = currentExpense?.subcategory ? BUDGET_SUBCATEGORY_CONFIG[currentExpense.subcategory as BudgetSubcategory] : null

                return (
                  <Row key={key} gutter={16} style={{ marginBottom: 16, padding: 16, border: '1px solid #f0f0f0', borderRadius: 6 }}>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        label="费用项目"
                        style={{ marginBottom: 0 }}>
                        <div style={{ fontWeight: 500 }}>
                          {subcategoryConfig ? getBudgetSubcategoryLabel(currentExpense.subcategory) : '-'}
                        </div>
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item
                        {...restField}
                        name={[name, 'quotedPrice']}
                        label={`单价 (${project?.currencySymbol || '¥'})`}
                        style={{ marginBottom: 0 }}>
                        <InputNumber
                          min={0}
                          precision={2}
                          placeholder="单价"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'personCount']}
                        label="人数"
                        style={{ marginBottom: 0 }}>
                        <InputNumber
                          min={1}
                          placeholder="人数"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'dayCount']}
                        label="天数"
                        style={{ marginBottom: 0 }}>
                        <InputNumber
                          min={1}
                          placeholder="天数"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item
                        {...restField}
                        name={[name, 'totalPrice']}
                        label={`总价 (${project?.currencySymbol || '¥'})`}

                        style={{ marginBottom: 0 }}>
                        <InputNumber
                          min={0}
                          precision={2}
                          placeholder="请输入总价"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'hasInvoice']}
                        label="有发票"
                        valuePropName="checked"
                        style={{ marginBottom: 0 }}>
                        <Switch />
                      </Form.Item>
                    </Col>
                  </Row>
                )
              })}
            </>
          )}
        </Form.List>
      </Form>
    </Drawer>
  )
}

export default AddNewBudgetExpenses
